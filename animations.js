// GSAP Hover Animations for Buttons and Menu Items
document.addEventListener("DOMContentLoaded", () => {
  // Wait for other animations to complete before applying hover effects
  setTimeout(() => {
    // Function to create slide-up text effect
    function createSlideUpEffect(element) {
      let span = element.querySelector("span");

      // If no span exists, create one and wrap the text content
      if (!span) {
        const originalText = element.textContent;
        element.innerHTML = `<span>${originalText}</span>`;
        span = element.querySelector("span");
      }

      if (span.querySelector(".text-original")) return; // Avoid double initialization

      // Create duplicate text for the animation
      const originalText = span.textContent;
      span.innerHTML = `
        <span class="text-original">${originalText}</span>
        <span class="text-hover">${originalText}</span>
      `;

      const originalSpan = span.querySelector(".text-original");
      const hoverSpan = span.querySelector(".text-hover");

      // Ensure text is visible by default
      gsap.set(span, {
        overflow: "hidden",
        height: "auto",
        position: "relative",
        display: "block",
      });

      // Set initial positions - make sure original text is visible
      gsap.set(originalSpan, {
        y: 0,
        position: "relative",
        display: "block",
      });
      gsap.set(hoverSpan, {
        y: "100%",
        position: "absolute",
        top: 0,
        left: 0,
        width: "100%",
      });

      // Create timeline for hover animation
      const tl = gsap.timeline({ paused: true });
      tl.to(originalSpan, { y: "-100%", duration: 0.3, ease: "power2.out" }).to(
        hoverSpan,
        { y: 0, duration: 0.3, ease: "power2.out" },
        0
      );

      return tl;
    }

    // Apply animations to get-in-touch buttons and CTA buttons
    const getInTouchBtns = document.querySelectorAll(
      ".get-in-touch-btn, .contact-button, .cta-button"
    );
    getInTouchBtns.forEach((btn) => {
      const timeline = createSlideUpEffect(btn);
      if (timeline) {
        btn.addEventListener("mouseenter", () => timeline.play());
        btn.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to menu buttons (separate from contact buttons)
    const menuBtns = document.querySelectorAll(
      ".sticky-menu-btn, #sticky-menu-btn"
    );
    menuBtns.forEach((btn) => {
      const timeline = createSlideUpEffect(btn);
      if (timeline) {
        btn.addEventListener("mouseenter", () => timeline.play());
        btn.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to fullscreen menu nav items only
    const menuNavItems = document.querySelectorAll(".custom-menu-nav a");
    menuNavItems.forEach((item) => {
      const timeline = createSlideUpEffect(item);
      if (timeline) {
        item.addEventListener("mouseenter", () => timeline.play());
        item.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to footer navigation links
    const footerNavItems = document.querySelectorAll(".footer-nav-link");
    footerNavItems.forEach((item) => {
      const timeline = createSlideUpEffect(item);
      if (timeline) {
        item.addEventListener("mouseenter", () => timeline.play());
        item.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to CTA button
    const ctaButton = document.querySelector(".cta-button");
    if (ctaButton) {
      const timeline = createSlideUpEffect(ctaButton);
      if (timeline) {
        ctaButton.addEventListener("mouseenter", () => timeline.play());
        ctaButton.addEventListener("mouseleave", () => timeline.reverse());
      }
    }

    // Apply animations to new styled CTA buttons (separate from get-in-touch)
    const styledCtaBtns = document.querySelectorAll(".cta-btn-styled");
    styledCtaBtns.forEach((btn) => {
      const timeline = createSlideUpEffect(btn);
      if (timeline) {
        btn.addEventListener("mouseenter", () => timeline.play());
        btn.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to navbar menu items (new-link a elements)
    const navbarItems = document.querySelectorAll(
      ".new-link a, .navbar-item-animated"
    );
    navbarItems.forEach((item) => {
      const timeline = createSlideUpEffect(item);
      if (timeline) {
        item.addEventListener("mouseenter", () => timeline.play());
        item.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to menu button (sticky-menu-btn)
    const menuButtons = document.querySelectorAll(
      "#sticky-menu-btn, .menu-btn-animated"
    );
    menuButtons.forEach((btn) => {
      const timeline = createSlideUpEffect(btn);
      if (timeline) {
        btn.addEventListener("mouseenter", () => timeline.play());
        btn.addEventListener("mouseleave", () => timeline.reverse());
      }
    });

    // Apply animations to footer navigation items
    const footerItems = document.querySelectorAll(
      ".footer-nav-link, .footer-item-animated"
    );
    footerItems.forEach((item) => {
      const timeline = createSlideUpEffect(item);
      if (timeline) {
        item.addEventListener("mouseenter", () => timeline.play());
        item.addEventListener("mouseleave", () => timeline.reverse());
      }
    });
  }, 2000); // Wait 2 seconds for preloader and other animations to complete

  // New Hero Section Animation
  const newHero = document.querySelector(".new-hero");
  const newHeroTitle = document.querySelector(".new-hero h1");

  if (newHero && newHeroTitle) {
    // Set initial state
    gsap.set(newHero, { opacity: 0 });
    gsap.set(newHeroTitle, {
      opacity: 0,
      y: 50,
      scale: 0.9,
    });

    // Animate hero section after preloader
    gsap.to(newHero, {
      opacity: 1,
      duration: 1,
      delay: 0.5,
      ease: "power2.out",
    });

    gsap.to(newHeroTitle, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 1.2,
      delay: 0.8,
      ease: "power3.out",
    });

    console.log("New hero animations initialized");
  }
});
