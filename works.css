/* Works Page Styles */

/* Page Setup */
.works-page {
  background: #fcfcfc !important;
  color: #111 !important;
  overflow: hidden !important;
  font-family: "Inter", "PP Neue Montreal", sans-serif !important;
  margin: 0 !important;
  padding: 0 !important;
  height: 100vh !important;
  width: 100vw !important;
}

/* Hide footer on works page */
.works-page .footer {
  display: none !important;
}

/* Reset any conflicting styles */
.works-page * {
  box-sizing: border-box;
}

/* Force visibility for works page */
.works-page,
.works-page .new-container,
.works-page .main-content,
.works-page .works-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.works-page .works-scroll {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.works-page .project-card {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Override any preloader hiding */
body.works-page .main-content {
  display: block !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

body.preloader-active.works-page .main-content {
  display: block !important;
}

/* Works Title - Absolute Positioned */
.works-title {
  position: fixed;
  top: 89%;
  left: 5rem;
  transform: translateY(-50%);
  z-index: 100;
  pointer-events: none;
}

.works-title h1 {
  font-size: clamp(3rem, 7vw, 10rem) !important;
  font-weight: 700;
  color: #111 !important;
  margin: 0;
  line-height: 0.9;
  /* letter-spacing: -0.05em; */
  /* writing-mode: vertical-rl; */
  text-orientation: mixed;
}

/* Scroll Progress Tracker */
.scroll-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1000;
}

.progress-bar {
  height: 100%;
  background: #111;
  width: 10%;
  transition: width 0.1s ease;
}

/* Override main style.css for works page */
.works-page .new-container {
  width: 100vw !important;
  height: 100vh !important;
  position: relative !important;
  background: #fcfcfc !important;
  display: block !important;
  opacity: 1 !important;
  transform: none !important;
}

/* Main Content - Override hidden styles */
.works-page .main-content {
  width: 100vw !important;
  height: 100vh !important;
  position: relative !important;
  background: #fcfcfc !important;
  display: block !important;
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Works Container */
.works-container {
  width: 100vw !important;
  height: 100vh !important;
  overflow-x: auto !important;
  overflow-y: hidden !important;
  position: relative !important;
  display: block !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Hide scrollbar but keep functionality */
.works-container::-webkit-scrollbar {
  display: none;
}

.works-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Horizontal Scroll Content */
.works-scroll {
  display: flex !important;
  flex-direction: row !important;
  height: 100vh !important;
  width: max-content !important;
  align-items: center !important;
  padding: 0 5rem !important;
  gap: 4rem !important;
  min-width: calc(
    60vw * 10 + 4rem * 9 + 10rem
  ) !important; /* Ensure it's wider than viewport */
}

/* Project Cards */
.project-card {
  flex-shrink: 0 !important;
  flex-grow: 0 !important;
  width: 40vw !important;
  height: 60vh !important;
  position: relative !important;
  cursor: pointer !important;
  transition: transform 0.3s ease !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  background: #333 !important;
  /* border: 2px solid #555 !important; */
  display: block !important;
}

.project-card:hover {
  transform: scale(1.02);
}

.project-image {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.project-card:hover .project-image img {
  transform: scale(1.05);
}

/* Project Info Overlay */
.project-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 4rem 3rem 3rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.project-card:hover .project-info {
  transform: translateY(0);
}

.project-info h3 {
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #fff;
}

.project-info p {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-weight: 300;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .works-title h1 {
    font-size: 10rem;
  }

  .project-card {
    width: 70vw;
    height: 75vh;
  }

  .works-scroll {
    gap: 3rem;
  }
}

@media (max-width: 900px) {
  .works-title {
    left: 2rem;
  }

  .works-title h1 {
    font-size: 8rem;
  }

  .project-card {
    width: 80vw;
    height: 70vh;
  }

  .works-scroll {
    padding: 0 2rem;
    gap: 2rem;
  }

  .project-info {
    padding: 3rem 2rem 2rem;
  }

  .project-info h3 {
    font-size: 2rem;
  }

  .project-info p {
    font-size: 1rem;
  }
}

@media (max-width: 600px) {
  .works-title {
    left: 1rem;
  }

  .works-title h1 {
    font-size: 6rem;
  }

  .project-card {
    width: 90vw;
    height: 65vh;
  }

  .works-scroll {
    padding: 0 1rem;
    gap: 1.5rem;
  }

  .project-info {
    padding: 2rem 1.5rem 1.5rem;
  }

  .project-info h3 {
    font-size: 1.5rem;
  }

  .project-info p {
    font-size: 0.9rem;
  }
}

/* Loading Animation */
.project-card {
  opacity: 1;
  transform: translateX(0);
  /* Temporarily disable animation for debugging */
  /* animation: fadeInSlide 0.6s ease forwards; */
}

.project-card:nth-child(1) {
  animation-delay: 0.1s;
}
.project-card:nth-child(2) {
  animation-delay: 0.2s;
}
.project-card:nth-child(3) {
  animation-delay: 0.3s;
}
.project-card:nth-child(4) {
  animation-delay: 0.4s;
}
.project-card:nth-child(5) {
  animation-delay: 0.5s;
}
.project-card:nth-child(6) {
  animation-delay: 0.6s;
}
.project-card:nth-child(7) {
  animation-delay: 0.7s;
}
.project-card:nth-child(8) {
  animation-delay: 0.8s;
}
.project-card:nth-child(9) {
  animation-delay: 0.9s;
}
.project-card:nth-child(10) {
  animation-delay: 1s;
}

@keyframes fadeInSlide {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Removed smooth scrolling for better performance */

/* Custom cursor for project cards */
.project-card::after {
  content: "View Project";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(42, 77, 59, 0.9);
  color: white;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.project-card:hover::after {
  opacity: 1;
}
