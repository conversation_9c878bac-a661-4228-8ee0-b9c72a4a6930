// Contact Sidebar Functionality
document.addEventListener("DOMContentLoaded", () => {
  const sidebar = document.getElementById("contact-sidebar");
  const sidebarOverlay = document.getElementById("sidebar-overlay");
  const sidebarClose = document.getElementById("sidebar-close");
  const contactForm = document.getElementById("contact-form");

  // Get all "Get in Touch" buttons
  const getInTouchButtons = document.querySelectorAll(
    ".get-in-touch-btn, #get-in-touch-btn, .contact-button, #mobile-contact, .get-in-touch-trigger"
  );

  // Function to open sidebar
  function openSidebar() {
    if (!sidebar) {
      console.error("Sidebar element not found!");
      return;
    }
    sidebar.classList.add("active");
    document.body.style.overflow = "hidden"; // Prevent background scrolling

    // GSAP animation for smooth opening
    if (typeof gsap !== "undefined") {
      gsap.fromTo(
        sidebar,
        { opacity: 0 },
        { opacity: 1, duration: 0.3, ease: "power2.out" }
      );

      gsap.fromTo(
        ".contact-sidebar-content",
        { x: "100%" },
        { x: "0%", duration: 0.4, ease: "power2.out", delay: 0.1 }
      );
    }
  }

  // Function to close sidebar
  function closeSidebar() {
    if (typeof gsap !== "undefined") {
      gsap.to(".contact-sidebar-content", {
        x: "100%",
        duration: 0.3,
        ease: "power2.in",
      });

      gsap.to(sidebar, {
        opacity: 0,
        duration: 0.3,
        delay: 0.1,
        ease: "power2.in",
        onComplete: () => {
          sidebar.classList.remove("active");
          document.body.style.overflow = ""; // Restore scrolling
        },
      });
    } else {
      sidebar.classList.remove("active");
      document.body.style.overflow = "";
    }
  }

  // Add click event listeners to all "Get in Touch" buttons
  getInTouchButtons.forEach((button) => {
    button.addEventListener("click", (e) => {
      e.preventDefault();
      openSidebar();
    });
  });

  // Close sidebar when clicking overlay
  sidebarOverlay.addEventListener("click", closeSidebar);

  // Close sidebar when clicking close button
  sidebarClose.addEventListener("click", closeSidebar);

  // Close sidebar with Escape key
  document.addEventListener("keydown", (e) => {
    if (e.key === "Escape" && sidebar.classList.contains("active")) {
      closeSidebar();
    }
  });

  // Handle form submission
  contactForm.addEventListener("submit", async (e) => {
    e.preventDefault();

    // Get form data
    const formData = new FormData(contactForm);
    const data = {
      name: formData.get("name"),
      email: formData.get("email"),
      phone: formData.get("phone"),
      projectType: formData.get("project-type"),
      message: formData.get("message"),
    };

    console.log("Form submitted:", data);

    // Get submit button and show loading state
    const submitBtn = contactForm.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;

    try {
      // EmailJS configuration
      const serviceID = 'service_dtpyjqm'; // Replace with your EmailJS service ID
      const templateID = 'template_ieymn73'; // Replace with your EmailJS template ID
      const publicKey = 'Pulnblf0CQbOTbPPg'; // Replace with your EmailJS public key

      // Prepare template parameters
      const templateParams = {
        from_name: data.name,
        from_email: data.email,
        phone: data.phone || 'Not provided',
        project_type: data.projectType || 'Not specified',
        message: data.message || 'No message provided',
        to_email: '<EMAIL>', // Your email address
      };

      // Send inquiry email to you
      const response = await emailjs.send(serviceID, templateID, templateParams, publicKey);
      console.log('Inquiry email sent successfully:', response);

      // Send confirmation email to customer
      const confirmationTemplateID = 'template_confirmation'; // You'll need to create this template
      const confirmationParams = {
        to_email: data.email, // Customer's email
        customer_name: data.name,
        project_type: data.projectType || 'Not specified',
        company_name: 'DIQRA Architects',
        company_email: '<EMAIL>',
        company_phone: '+****************'
      };

      try {
        const confirmationResponse = await emailjs.send(serviceID, confirmationTemplateID, confirmationParams, publicKey);
        console.log('Confirmation email sent successfully:', confirmationResponse);
      } catch (confirmationError) {
        console.log('Confirmation email failed (inquiry still sent):', confirmationError);
        // Don't throw error - main inquiry was successful
      }

      // Show success message
      showNotification('Thank you for your inquiry! We\'ll get back to you soon.', 'success');

      // Reset form and close sidebar
      contactForm.reset();
      setTimeout(() => {
        closeSidebar();
      }, 2000);

    } catch (error) {
      console.error('Error sending email:', error);

      // Show error message
      showNotification('Sorry, there was an error sending your message. Please try again or contact us directly.', 'error');
    } finally {
      // Reset button state
      submitBtn.textContent = originalText;
      submitBtn.disabled = false;
    }
  });

  // Function to show notifications
  function showNotification(message, type) {
    // Remove existing notifications
    const existingNotification = document.querySelector('.form-notification');
    if (existingNotification) {
      existingNotification.remove();
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `form-notification ${type}`;
    notification.textContent = message;

    // Add styles
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 5px;
      color: white;
      font-weight: 500;
      z-index: 10000;
      max-width: 400px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      background-color: ${type === 'success' ? '#10b981' : '#ef4444'};
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 5000);
  }

  // Add smooth animations to form elements
  if (typeof gsap !== "undefined") {
    // Animate form elements when sidebar opens
    const animateFormElements = () => {
      gsap.fromTo(
        ".form-section",
        { opacity: 0, y: 30 },
        {
          opacity: 1,
          y: 0,
          duration: 0.5,
          stagger: 0.1,
          delay: 0.3,
          ease: "power2.out",
        }
      );
    };

    // Override the openSidebar function to include form animations
    const originalOpenSidebar = openSidebar;
    openSidebar = function () {
      originalOpenSidebar();
      setTimeout(animateFormElements, 200);
    };
  }

  // Add hover effects to option cards
  const optionCards = document.querySelectorAll(".option-card");
  optionCards.forEach((card) => {
    card.addEventListener("mouseenter", () => {
      if (typeof gsap !== "undefined") {
        gsap.to(card, { scale: 1.02, duration: 0.2, ease: "power2.out" });
      }
    });

    card.addEventListener("mouseleave", () => {
      if (typeof gsap !== "undefined") {
        gsap.to(card, { scale: 1, duration: 0.2, ease: "power2.out" });
      }
    });
  });

  // Simple hover effects for sidebar elements (CSS handles the animations)
  function applySidebarHoverAnimations() {
    // Add subtle scale effect to interactive elements
    const interactiveElements = document.querySelectorAll(
      ".sidebar-header h2, .form-section h3, .sidebar-close, .submit-btn"
    );

    interactiveElements.forEach((element) => {
      element.addEventListener("mouseenter", () => {
        if (typeof gsap !== "undefined") {
          gsap.to(element, {
            scale: 1.02,
            duration: 0.2,
            ease: "power2.out",
          });
        }
      });

      element.addEventListener("mouseleave", () => {
        if (typeof gsap !== "undefined") {
          gsap.to(element, {
            scale: 1,
            duration: 0.2,
            ease: "power2.out",
          });
        }
      });
    });
  }

  // Apply hover animations after sidebar opens
  const originalOpenSidebar = openSidebar;
  openSidebar = function () {
    originalOpenSidebar();
    setTimeout(() => {
      applySidebarHoverAnimations();
      animateFormElements();
    }, 300);
  };
});
