@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

@font-face {
  src: url("https://fonts.cdnfonts.com/css/pp-neue-montreal") format("woff2");
  font-family: "PP Neue Montreal", sans-serif;
  font-weight: 400;
}

/* CSS Variables */
:root {
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --color-text-primary: #111;
}

*,
*:before,
*:after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: calc(100vw / 1512 * 10);
}

body {
  font-family: "PP Neue Montreal", sans-serif;
  font-weight: 500;
  font-size: 1.8rem;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* background-color: #f5f5f5; */
  background: #fff;
  text-transform: uppercase;
  letter-spacing: -0.02em;
  margin: 0;
  padding: 0;
}

/* Preloader active state */
body.preloader-active {
  overflow: hidden;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Post-preloader state - enable scrolling */
body.preloader-complete {
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
  display: block;
}

body::before {
  content: "";
  position: fixed;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: transparent
    url("http://assets.iceable.com/img/noise-transparent.png") repeat 0 0;
  background-size: 300px 300px;
  animation: noise-animation 0.3s steps(5) infinite;
  opacity: 0.03;
  will-change: transform;
  z-index: 100;
  pointer-events: none;
}

@keyframes noise-animation {
  0% {
    transform: translate(0, 0);
  }

  10% {
    transform: translate(-2%, -3%);
  }

  20% {
    transform: translate(-4%, 2%);
  }

  30% {
    transform: translate(2%, -4%);
  }

  40% {
    transform: translate(-2%, 5%);
  }

  50% {
    transform: translate(-4%, 2%);
  }

  60% {
    transform: translate(3%, 0);
  }

  70% {
    transform: translate(0, 3%);
  }

  80% {
    transform: translate(-3%, 0);
  }

  90% {
    transform: translate(2%, 2%);
  }

  100% {
    transform: translate(1%, 0);
  }
}

/* New Preloader Styles */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #e6f0ec;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  flex-direction: column;
}

.preloader__content {
  position: relative;
  width: 450px;
  height: 280px;
  overflow: hidden;
  will-change: transform, filter;
}

.preloader__text-container {
  position: relative;
  display: flex;
  gap: 0.5rem;
  z-index: 9999;
  color: #111;
  font-size: 2rem;
  font-weight: 600;
  filter: blur(0px);
  will-change: transform, filter;
  margin-bottom: 2rem;
}

.preloader__text-cosmic,
.preloader__text-reflections {
  will-change: transform, color;
}

.preloader__image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transform: scale(0.05);
  transform-origin: center center;
}

.preloader__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(44, 62, 80, 0.3);
  z-index: 2;
}

/* Dynamic Navbar System */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 2rem 4rem;
  z-index: 100;
  font-size: 1.6rem;
  letter-spacing: -0.02em;
  color: black;
  transition: all 0.3s ease;
  opacity: 1;
}

.navbar-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-logo {
  font-weight: 700;
  font-size: 1.8rem;
  opacity: 0;
  transform: translateY(-20px);
}

.navbar-logo img {
  height: 40px;
  width: auto;
  object-fit: contain;
}

.navbar-contact {
  opacity: 0;
  transform: translateY(-20px);
}

.get-in-touch-btn {
  display: inline-flex;
  /* padding: 0.8rem 2rem; */
  padding: 0.8rem 1.5rem;
  background: #080807;
  /* background: rgba(255, 255, 255, 0.1); */
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
}

.get-in-touch-btn span {
  color: #fff;
  display: block;
  position: relative;
}

.contact-button span {
  color: #fff;
  display: block;
  position: relative;
}

.get-in-touch-btn:hover {
  background: #080807;
  color: #fff;
}

/* New classes for navbar items, menu button, and footer items with get-in-touch animation */
.navbar-item-animated {
  display: inline-flex;
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

.navbar-item-animated span {
  color: #fff;
  display: block;
  position: relative;
}

.menu-btn-animated {
  display: inline-flex;
  padding: 0.8rem 1.5rem;
  background: #080807;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.4rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

.menu-btn-animated span {
  color: #fff;
  display: block;
  position: relative;
}

.menu-btn-animated:hover {
  background: #080807;
  color: #fff;
}

.footer-item-animated {
  display: inline-flex;
  color: #fff;
  text-decoration: none;
  font-weight: 500;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

.footer-item-animated span {
  color: #fff;
  display: block;
  position: relative;
}

/* Menu Button (appears when scrolling) */
.menu-button {
  position: fixed;
  top: 2rem;
  right: 4rem;
  z-index: 101;
  background: rgba(128, 128, 128, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(128, 128, 128, 0.3);
  border-radius: 50px;
  padding: 1rem 2rem;
  color: white;
  font-weight: 600;
  font-size: 1.4rem;
  cursor: pointer;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.menu-button.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.menu-button:hover {
  background: rgba(96, 96, 96, 0.95);
}

/* Hide navbar when scrolled */
.navbar.hide {
  opacity: 0;
  pointer-events: none;
}

/* Ensure navbar is visible on desktop when not hidden */
@media (min-width: 1025px) {
  .navbar:not(.hide) {
    opacity: 1;
    pointer-events: auto;
  }

  .navbar:not(.hide) .navbar-logo,
  .navbar:not(.hide) .navbar-menu,
  .navbar:not(.hide) .navbar-contact {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fullscreen Menu Overlay */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  z-index: 200;
  display: flex;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

.menu-overlay.active {
  opacity: 1;
  pointer-events: auto;
}

.menu-content {
  display: flex;
  width: 100%;
  height: 100%;
}

.menu-left {
  flex: 1;
  padding: 8rem 6rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.menu-right {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.menu-nav {
  list-style: none;
}

.menu-nav li {
  margin-bottom: 2rem;
}

.menu-nav a {
  color: white;
  text-decoration: none;
  font-size: clamp(3rem, 6vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  display: block;
  transition: opacity 0.3s ease;
  position: relative;
}

.menu-nav a:hover {
  opacity: 0.7;
}

.menu-close {
  position: absolute;
  top: 2rem;
  right: 4rem;
  background: none;
  border: none;
  color: white;
  font-size: 3rem;
  cursor: pointer;
  z-index: 201;
  transition: opacity 0.3s ease;
}

.menu-close:hover {
  opacity: 0.7;
}

/* Menu Hover Images */
.menu-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 60%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.5s ease;
  border-radius: 10px;
}

.menu-image.active {
  opacity: 1;
}

/* Main Content Styles */
.main-content {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.main-content.hidden {
  display: none;
}

.main-content.fade-in {
  opacity: 1;
  transform: translateY(0);
}

/* Section Styles */
.section-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  margin-bottom: 3rem;
  text-align: center;
}

/* Traditional Hero Section */
.hero-section {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  opacity: 0;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-background img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 2rem;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.1;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.7);
}

.hero-subtitle {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 400;
  margin-bottom: 1.5rem;
  opacity: 0.9;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
}

.hero-description {
  font-size: clamp(1.1rem, 2vw, 1.3rem);
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.8;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.7);
}

.hero-cta {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.cta-button {
  background: #2a4d3b;
  color: white;
  border: none;
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-button:hover {
  background: #111;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42, 77, 59, 0.3);
}

.cta-link {
  color: white;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.cta-link:hover {
  border-bottom-color: white;
  transform: translateY(-1px);
}

/* Main Content */
.main-content {
  position: relative;
  background: #fff;
  z-index: 5;
  display: none;
}

/* Hide main content and footer initially */
body.preloader-active .main-content,
body.preloader-active .footer {
  display: none;
}

/* Show main content and footer after preloader completes */
body.preloader-complete .main-content,
body.preloader-complete .footer {
  display: block;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 3rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  z-index: 4;
  opacity: 0;
}

.scroll-indicator span {
  font-size: 1.2rem;
  font-weight: 500;
  margin-top: 1rem;
  letter-spacing: 0.1em;
}

.scroll-arrow {
  width: 2px;
  height: 3rem;
  background: white;
  position: relative;
  animation: scrollBounce 2s ease-in-out infinite;
}

.scroll-arrow::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: -6px;
  width: 14px;
  height: 14px;
  border-right: 2px solid white;
  border-bottom: 2px solid white;
  transform: rotate(45deg);
}

@keyframes fadeInBounce {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes scrollBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(10px);
  }
}

/* About Section - OH Architecture inspired */
.about-section {
  padding: 0;
  /* background: #f8f8f8; */
  color: #111;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.about-content {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr;
  /* min-height: 100vh; */
  background: #fff;
}

.about-text {
  padding: 8rem 6rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #f8f8f8;
}

.about-text h2 {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  margin-bottom: 3rem;
  line-height: 1.1;
}

.about-description {
  font-size: clamp(1.4rem, 2.5vw, 2rem);
  line-height: 1.6;
  margin-bottom: 2rem;
  text-transform: none;
  opacity: 0.8;
  font-weight: 400;
}

.about-cta {
  margin-top: 3rem;
}

.about-cta a {
  display: inline-block;
  padding: 1.5rem 3rem;
  background: #111;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.4rem;
  transition: background 0.3s ease;
}

.about-cta a:hover {
  background: #333;
}

/* PLAN B - Integrated Services Highlight within About Section */
.about-services-highlight {
  margin: 3rem 0;
  padding: 2.5rem 0;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.services-highlight-title {
  font-size: 1.6rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 2rem;
  text-align: left;
  letter-spacing: -0.01em;
}

.services-highlight-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.service-highlight-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  text-decoration: none;
  color: inherit;
}

.service-highlight-item:hover {
  background: #f5f5f5;
  border-left-color: #111;
  transform: translateX(5px);
}

.service-highlight-icon {
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.service-highlight-item:hover .service-highlight-icon {
  transform: scale(1.1);
}

.service-highlight-name {
  font-size: 1.3rem;
  font-weight: 500;
  color: #111;
  letter-spacing: -0.01em;
}

/* Responsive Design for Integrated Services */
@media (max-width: 768px) {
  .about-services-highlight {
    margin: 2rem 0;
    padding: 2rem 0;
  }

  .services-highlight-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .service-highlight-item {
    padding: 0.8rem;
  }

  .service-highlight-name {
    font-size: 1.2rem;
  }

  .service-highlight-icon {
    width: 35px;
    height: 35px;
    font-size: 1.3rem;
  }
}

.about-image-wrapper {
  position: relative;
  overflow: hidden;
}

.about-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.about-image:hover {
  transform: scale(1.05);
}

.second-about {
  display: grid;
  padding: 20px;
  background: #fff;
  grid-template-columns: 400px 1fr;
  grid-template-rows: auto;
  min-height: 400px;
  gap: 4rem;
  align-items: start;
  margin-bottom: 4rem;
}
.leftabout {
  height: 400px;
}

.leftabout img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.rightabout {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.rightabout img {
  width: 100%;
  height: auto;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.about-second-section-content {
  font-size: 44px;
  line-height: 1.1;
  font-weight: 700;
  text-transform: none;
  color: #000;
  margin-top: 2rem;
  position: relative;
  z-index: 2;
  display: block;
  width: 100%;
}

/* Service Pages Content Styles */
.service-content {
  padding: 8rem 0;
  background: #fff;
}

.service-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 4rem;
}

.service-text {
  max-width: 800px;
  margin: 0 auto;
  text-align: left;
}

.service-text h2 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: #111;
  margin-bottom: 3rem;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.service-text p {
  font-size: 1.4rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 2rem;
}

.service-text p:last-child {
  margin-bottom: 0;
}

/* Responsive service content */
@media (max-width: 768px) {
  .service-content {
    padding: 4rem 0;
  }

  .service-container {
    padding: 0 2rem;
  }

  .service-text h2 {
    margin-bottom: 2rem;
  }

  .service-text p {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }
}

/* Our Process Section - 2 Column Grid */
.services-section {
  position: relative;
  background: #f8f8f8;
  color: #111;
  padding: 12rem 0;
}

.services-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
}

/* 2 Column Grid Layout */
.process-grid {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 6rem;
  align-items: start;
}

/* Left Column: Image + Process Steps */
.process-left {
  width: 400px;
}

.process-image {
  margin-bottom: 3rem;
}

.process-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 0;
  filter: grayscale(100%);
}

.process-steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.process-step {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #ddd;
}

.step-number {
  font-size: 1.2rem;
  font-weight: 600;
  color: #666;
  min-width: 2rem;
}

.step-title {
  font-size: 1.4rem;
  font-weight: 500;
  color: #111;
  margin: 0;
}

/* Right Column: Bold Text + CTA */
.process-right {
  display: flex;
  align-items: center;
  min-height: 400px;
}

.process-content {
  width: 100%;
}

.process-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 3rem;
  color: #111;
}

.process-cta {
  margin-top: 3rem;
}

.process-cta .cta-button {
  display: inline-flex;
  padding: 0.8rem 1.5rem;
  background: #080807;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
}

.process-cta .cta-button span {
  color: #fff;
  display: block;
  position: relative;
}

.process-cta .cta-button:hover {
  background: #080807;
  color: #fff;
}

.step-number {
  font-size: 1.2rem;
  font-weight: 500;
  opacity: 0.6;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.step-title {
  font-size: clamp(1.8rem, 3vw, 2.5rem);
  font-weight: 400;
  line-height: 1.3;
  margin-bottom: 1.5rem;
}

.step-description {
  font-size: 1.4rem;
  line-height: 1.6;
  opacity: 0.7;
  text-transform: none;
}

/* Featured Projects Section */
.projects-section {
  padding: 8rem 0;
  background: #111;
  color: white;
}

.projects-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 4rem;
}

.projects-section .section-title {
  text-align: center;
  margin-bottom: 6rem;
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  letter-spacing: -0.02em;
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  margin-bottom: 6rem;
}

.project-item {
  cursor: pointer;
  transition: transform 0.3s ease;
}

.project-item:hover {
  transform: translateY(-10px);
}

.project-image-wrapper {
  margin-bottom: 2rem;
  overflow: hidden;
  border-radius: 0;
}

.project-image {
  width: 100%;
  height: 350px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.project-item:hover .project-image {
  transform: scale(1.05);
}

.project-info {
  text-align: left;
}

.project-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: color 0.3s ease;
}

.project-item:hover .project-title {
  color: #ccc;
}

.project-category {
  font-size: 1rem;
  opacity: 0.6;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

.project-description {
  font-size: 1.2rem;
  opacity: 0.8;
  line-height: 1.6;
}

/* View More Button */
.projects-cta {
  text-align: center;
}

.view-more-btn {
  display: inline-flex;
  padding: 0.8rem 1.5rem;
  background: #080807;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  color: white;
  text-decoration: none;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
  overflow: hidden;
  white-space: nowrap;
}

.view-more-btn span {
  color: #fff;
  display: block;
  position: relative;
}

.view-more-btn:hover {
  background: #080807;
  color: #fff;
  transform: translateY(-2px);
}

/* Contact Section */
.contact-section {
  padding: 8rem 6rem;
  background: #f5f5f5;
  color: #111;
}

.contact-content {
  width: 100%;
  text-align: center;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
}

.contact-item h3 {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.contact-item p {
  font-size: 1.4rem;
  text-transform: none;
  opacity: 0.8;
  line-height: 1.6;
}

/* Responsive Design */

/* Tablet Responsive (769px - 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
  /* Navbar responsive for tablets */
  .navbar {
    padding: 2rem 3rem;
  }

  .navbar-menu {
    display: none;
  }

  .navbar-contact {
    display: none;
  }

  .menu-button {
    top: 2rem;
    right: 3rem;
    padding: 1rem 2rem;
    font-size: 1.4rem;
    /* Ensure menu button is always visible on tablet */
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }

  .contact-button {
    top: 2rem;
    right: 17rem;
    padding: 1rem 2rem;
    font-size: 1.4rem;
    /* Ensure contact button is always visible on tablet */
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }

  /* Fullscreen menu tablet */
  .menu-left {
    padding: 6rem 4rem;
  }

  .menu-nav a {
    font-size: clamp(3rem, 6vw, 5rem);
  }

  /* Custom menu nav tablet styles */
  .custom-menu-nav a {
    font-size: clamp(3rem, 6vw, 5rem) !important;
  }

  .menu-close {
    top: 2rem;
    right: 3rem;
    font-size: 2.5rem;
  }

  /* Hero adjustments for tablet */
  .hero-title {
    font-size: clamp(4rem, 8vw, 8rem);
  }

  .hero-subtitle {
    font-size: clamp(2rem, 4vw, 4rem);
  }

  /* Hero section responsive */
  .hero-content {
    padding: 1.5rem;
  }

  .hero-cta {
    flex-direction: column;
    gap: 1.5rem;
  }

  .cta-section .cta-button.get-in-touch-btn {
    padding: 1rem 2rem !important;
    font-size: 1rem !important;
  }

  /* Footer responsive tablet */
  .footer {
    padding: 4rem 3rem 2rem;
    margin-top: 6rem;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .footer-left,
  .footer-right {
    justify-content: center;
  }

  .footer-address {
    text-align: center;
  }

  .footer-nav {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
  }

  .footer-nav-link {
    font-size: 1.4rem;
  }

  .footer-nav-link::after {
    display: none;
  }

  /* Process page responsive tablet */
  .process-hero {
    padding: 8rem 3rem 6rem;
  }

  .process-hero-content {
    grid-template-columns: 1fr;
    gap: 4rem;
    text-align: center;
  }

  .process-title {
    font-size: 3rem;
  }

  .approach-section {
    padding: 6rem 3rem;
  }

  .parallax-title {
    font-size: 3rem;
  }

  .process-section {
    padding: 3rem 2rem;
  }

  .item {
    flex-direction: column;
    height: auto;
    min-height: 40vh;
    padding: 1rem;
    border-radius: 10px;
  }

  .spacer {
    display: none;
  }

  .content {
    flex: 1 1 100%;
    width: 100%;
    padding: 0;
  }

  .content h1 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
  }

  .content p {
    font-size: 1rem;
  }

  .intro {
    padding: 0 1rem;
    margin-bottom: 30px;
  }

  .intro h1 {
    font-size: 2.5rem;
  }

  .testimonials-section {
    padding: 6rem 3rem;
  }

  .testimonials-content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }
}

/* Mobile Responsive (768px and below) */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    min-height: auto;
  }

  .about-text {
    padding: 4rem 2rem;
    text-align: left;
  }

  .about-text h2 {
    font-size: clamp(2.5rem, 8vw, 4rem);
    margin-bottom: 2rem;
  }

  .about-text p {
    font-size: 1.2rem;
    line-height: 1.6;
  }

  .about-image-wrapper {
    min-height: 50vh;
  }

  .projects-container {
    padding: 0 2rem;
  }

  .projects-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .project-image {
    height: 250px;
  }

  .project-title {
    font-size: 1.5rem;
  }

  .contact-info {
    grid-template-columns: 1fr;
  }

  .contact-section {
    padding: 4rem 2rem;
  }

  .header-inner {
    flex-direction: column;
    gap: 2rem;
  }

  .header-middle {
    gap: 2rem;
  }

  /* Hero overlay adjustments for mobile */
  .hero-content {
    padding: 1rem;
  }

  .hero-title {
    font-size: clamp(2.5rem, 8vw, 6rem);
  }

  .hero-subtitle {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  /* Navbar responsive */
  .navbar {
    padding: 1.5rem 2rem;
  }

  .navbar-menu {
    display: none;
  }

  .navbar-contact {
    display: none;
  }

  .menu-button {
    top: 1.5rem;
    right: 2rem;
    padding: 0.8rem 1.5rem;
    font-size: 1.2rem;
    /* Ensure menu button is always visible on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }

  .contact-button {
    top: 1.5rem;
    right: 12rem;
    padding: 0.8rem 1.5rem;
    font-size: 1.2rem;
    /* Ensure contact button is always visible on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
  }

  /* Fullscreen menu mobile */
  .menu-left {
    padding: 6rem 2rem;
  }

  .menu-content {
    flex-direction: column;
  }

  .menu-right {
    display: none;
  }

  /* Fullscreen menu mobile */
  .menu-left {
    padding: 6rem 2rem;
  }

  .menu-nav li {
    margin-bottom: 3rem;
  }

  .menu-nav a {
    font-size: clamp(3.5rem, 10vw, 6rem);
  }

  /* Custom menu nav mobile styles */
  .custom-menu-nav a {
    font-size: clamp(3.5rem, 10vw, 6rem) !important;
  }

  .menu-close {
    top: 1.5rem;
    right: 2rem;
    font-size: 2rem;
  }

  /* Our Process section mobile */
  .services-section {
    padding: 6rem 0;
  }

  .services-container {
    padding: 0 1.5rem;
  }

  /* Mobile: Stack columns vertically */
  .process-grid {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .process-left {
    width: 100%;
  }

  .process-image img {
    height: 250px;
  }

  .process-steps {
    gap: 1rem;
  }

  .process-step {
    gap: 0.8rem;
  }

  .step-number {
    font-size: 1rem;
    min-width: 1.5rem;
  }

  .step-title {
    font-size: 1.2rem;
  }

  .process-right {
    min-height: auto;
  }

  .process-title {
    font-size: clamp(2rem, 6vw, 3rem);
    margin-bottom: 2rem;
  }

  .process-cta {
    margin-top: 2rem;
  }

  .cta-button {
    font-size: 1.2rem;
  }

  .step-title {
    font-size: clamp(1.6rem, 4vw, 2rem);
    margin-bottom: 1rem;
  }

  .step-description {
    font-size: 1.2rem;
  }

  /* Contact sidebar mobile */
  .contact-sidebar-content {
    width: 100%;
    max-width: 100vw;
  }

  .sidebar-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
  }

  .sidebar-header h2 {
    font-size: 1.5rem;
  }

  .sidebar-body {
    padding: 1.5rem;
  }

  .sidebar-subtitle {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }

  .input-group {
    margin-bottom: 1.25rem;
  }

  .input-group input,
  .input-group select,
  .input-group textarea {
    padding: 0.875rem;
    font-size: 0.95rem;
  }

  .input-group label {
    font-size: 0.95rem;
  }

  .submit-btn {
    padding: 0.875rem 1.5rem;
    font-size: 0.95rem;
  }

  /* Testimonials page mobile */
  .testimonials-hero {
    padding: 6rem 1.5rem 3rem 1.5rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 2rem;
    margin-top: 3rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .featured-testimonial {
    padding: 4rem 1.5rem;
  }

  .featured-content {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .featured-quote blockquote {
    font-size: 1.4rem;
  }

  .featured-project img {
    height: 300px;
  }

  .testimonials-grid {
    padding: 4rem 1.5rem;
  }

  .grid-header h2 {
    font-size: 2rem;
  }

  .testimonials-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .testimonial-card {
    padding: 2rem;
  }

  .testimonials-cta {
    padding: 4rem 1.5rem;
  }

  .testimonials-cta h2 {
    font-size: 2rem;
  }

  .testimonials-cta p {
    font-size: 1.1rem;
  }

  .contact-form {
    padding: 1.5rem;
    gap: 1.5rem;
  }

  .form-section h3 {
    font-size: 1.2rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group input {
    padding: 0.8rem;
  }

  .option-card {
    padding: 0.8rem;
  }

  .submit-btn {
    padding: 1rem 1.5rem;
  }

  /* Footer responsive mobile */
  .footer {
    padding: 3rem 2rem 1.5rem;
    margin-top: 4rem;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 2.5rem;
    text-align: center;
  }

  .footer-left,
  .footer-right {
    justify-content: center;
  }

  .footer-logo img {
    height: 3rem;
  }

  .footer-address {
    text-align: center;
  }

  .footer-address h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
  }

  .footer-address p {
    font-size: 1.1rem;
  }

  .footer-nav {
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .footer-nav-link {
    font-size: 1.3rem;
  }

  .footer-nav-link::after {
    display: none;
  }

  .footer-bottom {
    margin-top: 3rem;
    padding-top: 1.5rem;
  }

  .footer-bottom p {
    font-size: 1.1rem;
  }

  /* About section mobile improvements */
  .about-text {
    padding: 3rem 1.5rem;
  }

  .about-text h2 {
    font-size: clamp(2rem, 7vw, 3rem);
    margin-bottom: 1.5rem;
  }

  .about-text p {
    font-size: 1.1rem;
  }

  /* Process page responsive mobile */
  .process-hero {
    padding: 6rem 2rem 4rem;
  }

  .process-title {
    font-size: 2.5rem;
  }

  .process-hero-image img {
    height: 40vh;
  }

  .approach-section {
    padding: 4rem 2rem;
  }

  .approach-text p {
    font-size: 1.4rem;
  }

  .parallax-title {
    font-size: 2.5rem;
  }

  .process-section {
    padding: 2rem 1rem;
  }

  .item {
    min-height: 30vh;
    padding: 0.5rem;
    border-radius: 6px;
  }

  .content h1 {
    font-size: 1.5rem;
  }

  .content p {
    font-size: 0.95rem;
  }

  .intro h1 {
    font-size: 2rem;
  }

  .intro p {
    font-size: 1.2rem;
  }

  .testimonials-section {
    padding: 4rem 2rem;
  }

  .testimonial-quote {
    font-size: 1.4rem;
  }

  .testimonial-project {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

/* PROCESS PAGE STYLES */

/* Ensure process page content is visible */
.process-page .main-content {
  display: block !important;
}

.process-page .footer {
  display: block !important;
}

/* Process Hero Section */
.process-hero {
  padding: 12rem 4rem 8rem;
  max-width: 1400px;
  margin: 0 auto;
}

.process-hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6rem;
  align-items: center;
}

.process-title {
  font-size: 4rem;
  font-weight: 700;
  line-height: 1.1;
  color: #111;
  margin: 0;
}

.process-hero-image img {
  width: 100%;
  height: 60vh;
  object-fit: cover;
  border-radius: 8px;
}

/* Approach Section */
.approach-section {
  padding: 8rem 4rem;
  max-width: 1400px;
  margin: 0 auto;
}

.approach-content {
  max-width: 800px;
}

.approach-heading {
  font-size: 1.8rem;
  font-weight: 600;
  color: #666;
  margin-bottom: 3rem;
  text-transform: lowercase;
}

.approach-text p {
  font-size: 1.6rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 2rem;
  font-weight: 400;
}

.approach-cta {
  margin-top: 4rem;
}

/* Parallax Overview Section */
.parallax-overview {
  position: relative;
  height: 100vh;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.parallax-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120%;
  z-index: 1;
}

.parallax-bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.parallax-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.parallax-title {
  font-size: 4rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.parallax-arrow {
  font-size: 2rem;
  opacity: 0.8;
}

/* Process Hero Text Section */
.process-hero-text {
  padding: 8rem 4rem 4rem;
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
}

.process-main-title {
  font-size: 4rem;
  font-weight: 700;
  color: #111;
  line-height: 1.1;
  margin-bottom: 2rem;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.process-subtitle {
  font-size: 1.4rem;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* Process Hero Banner */
.process-hero-banner {
  width: 100%;
  height: 70vh;
  overflow: hidden;
  margin-bottom: 6rem;
}

.hero-banner-image {
  width: 100%;
  height: 100%;
}

.hero-banner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Process Page Styles */
#intro {
  padding: 100px 0 40px 0;
  text-align: center;
}

#intro h1 {
  /* font-size: 3rem; */
  font-size: 42px;
  font-weight: 700;
  color: #111;
  max-width: 1000px;
  margin: 0 auto 2rem;
  line-height: 1.2;
  text-transform: none;
}

#intro img {
  width: 60vw;
  max-width: 700px;
  border-radius: 20px;
  margin: 2rem 0;
}

#intro p {
  max-width: 800px;
  margin: 0 auto;
  font-size: 16px;
  text-transform: none;
  color: #111;
  line-height: 1.6;
  font-weight: 400;
}

.grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  align-items: flex-start;
  gap: 60px;
  margin-bottom: 120px;
}

.pin-content-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.pin-text {
  background: #fff;
  border-radius: 16px;
  padding: 40px 32px;
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.04);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.pin-text h2 {
  font-size: 4rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 1.5rem;
}

.pin-text p {
  font-size: 1.5rem;
  color: #333;
  line-height: 1.6;
  max-width: 100%;
}

/* Individual pin-content styling for better visual hierarchy */

.pin-text::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 60px;
  /* background: #1a3a2a; */
  background: #111;
  border-radius: 2px;
}

/* Add smooth scroll behavior for better UX */
.pin-container {
  scroll-behavior: smooth;
}

/* Ensure proper spacing and alignment */
.pin-text h2 {
  position: relative;
  padding-left: 20px;
}

.pin-text p {
  position: relative;
  padding-left: 20px;
}

.pin-image-sticky {
  position: sticky;
  top: 80px;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pin-image-stack {
  position: relative;
  width: 100%;
  height: 70vh;
}

.pin-image-stack img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.5s, transform 0.5s;
  transform: scale(0.95) translateY(50px);
  z-index: 1;
  border-radius: 20px;
  box-shadow: 0 2px 24px 0 rgba(0, 0, 0, 0.08);
}

.pin-image-stack img.active {
  opacity: 1;
  transform: scale(1) translateY(0);
  z-index: 2;
}

.parallax-container {
  position: relative;
  overflow: hidden;
  height: 100vh;
  width: 100%;
  background: #fff;
  perspective: 1px;
  transform-style: preserve-3d;
  overflow-x: hidden;
  margin-bottom: 80px;
}

.parallax-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform: translateZ(-1px) scale(2);
  transition: transform 0.3s ease;
  z-index: 1;
}

.parallax-image img {
  width: 100%;
  height: 100vh;
  object-fit: cover;
  display: block;
}

.parallax-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  z-index: 10;
  text-shadow: 0 2px 24px rgba(0, 0, 0, 0.3);
}

.parallax-text h2 {
  font-size: 42px;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.parallax-text p {
  font-size: 1.4rem;
  max-width: 800px;
  line-height: 1.6;
  text-transform: none;
  margin: 0 auto;
}

.px-5 {
  padding-left: 5rem;
  padding-right: 5rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
}

.col-8 {
  width: 66.666667%;
}

.offset-2 {
  margin-left: 16.666667%;
}

.pb-5 {
  padding-bottom: 5rem;
}
.pin-end-text {
  font-size: 16px;
  line-height: 1.6;
  text-transform: none;
  color: #111;
  font-weight: 400;
}

/* Process page navbar adjustments */
.process-page .navbar {
  display: flex;
  width: 100%;
  padding: 0 4rem;
}

.process-page .navbar-inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.process-page .menu-button {
  display: block;
}

.process-page .contact-button {
  display: block;
}

/* Ensure process page content is visible */
.process-page .main-content {
  display: block !important;
}

.process-page .footer {
  display: block !important;
}

/* Process Page Responsive */
@media (max-width: 768px) {
  .process-hero-text {
    padding: 6rem 2rem 3rem;
  }

  .process-main-title {
    font-size: 2.5rem;
  }

  .process-subtitle {
    font-size: 1.2rem;
  }

  .process-hero-banner {
    height: 50vh;
    margin-bottom: 4rem;
  }
}

@media (max-width: 600px) {
  .item {
    flex-direction: column;
    height: auto;
    min-height: 40vh;
    padding: 1rem;
    border-radius: 10px;
  }
  .spacer {
    display: none;
  }
  .content {
    flex: 1 1 100%;
    width: 100%;
    padding: 0;
  }
  .content h1 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
  }
  .content p {
    font-size: 1rem;
  }
  .intro {
    padding: 0 1rem;
    margin-bottom: 30px;
  }
}

@media (max-width: 480px) {
  .item {
    min-height: 30vh;
    padding: 0.5rem;
    border-radius: 6px;
  }
  .content h1 {
    font-size: 1.1rem;
  }
  .content p {
    font-size: 0.95rem;
  }
  .intro h1 {
    font-size: 1.2rem;
  }
}

/* Testimonials Section */
.testimonials-section {
  padding: 8rem 4rem;
  background: #fff;
}

.testimonials-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 6rem;
  align-items: start;
}

.testimonial-image img {
  width: 100%;
  height: 70vh;
  object-fit: cover;
  border-radius: 8px;
}

.testimonial-heading {
  font-size: 1.6rem;
  color: #666;
  margin-bottom: 3rem;
  text-transform: lowercase;
}

.testimonial-quote {
  font-size: 1.6rem;
  line-height: 1.6;
  color: #333;
  font-style: italic;
  margin-bottom: 3rem;
  border: none;
  padding: 0;
}

.testimonial-author {
  margin-bottom: 3rem;
}

.author-name {
  font-size: 1.4rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 0.5rem;
}

.author-project {
  font-size: 1.2rem;
  color: #666;
}

.testimonial-project {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.project-images {
  display: flex;
  gap: 1rem;
}

.project-images img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.project-info h4 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 0.5rem;
}

.project-info p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.project-link {
  color: #111;
  text-decoration: none;
  font-weight: 500;
  border-bottom: 1px solid #111;
  transition: opacity 0.3s ease;
}

.project-link:hover {
  opacity: 0.7;
}

/* FOOTER STYLES */
.footer {
  background: #111;
  color: #fff;
  padding: 6rem 4rem 2rem;
  margin-top: 8rem;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* display: flex;
  justify-content: space-around; */
  gap: 4rem;
  align-items: start;
}

/* Footer Left - Logo */
.footer-left {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
}

.footer-logo {
  /* height: 4rem; */
  width: auto;
}

.footer-logo-image1 {
  height: 450px;
  /* width: auto; */
  width: 450px;

  margin-right: 1rem;
  margin-bottom: 3rem;
}

.footer-logo-image2 {
  /* height: 4rem; */
  width: 200px;
  filter: brightness(0) invert(1);
}

/* Footer Center - Navigation */
.footer-center {
  display: flex;
  margin-left: 5rem;
}

.footer-nav {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  /* align-items: center; */
}

.footer-nav-link {
  color: #fff;
  text-decoration: none;
  font-size: 4rem;
  font-weight: 400;
  transition: color 0.5s;
  position: relative;
  white-space: nowrap;
  text-transform: capitalize;
  display: inline-block;
  will-change: transform;
}

.footer-nav-link:hover {
  color: #fff;
}

/* Footer Right - Address */
.footer-right {
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
  margin-right: 8rem;
  padding-right: 8rem;
}
.footer-acknowledgement {
  margin-bottom: 2rem;
}

.footer-acknowledgement h1 {
  font-size: 12px;
  margin-bottom: 2rem;
}

.footer-acknowledgement p {
  font-size: 12px;
  text-transform: uppercase;
  color: #fff;
  margin-bottom: 5rem;
}

.footer-address {
  /* text-align: right; */
}

.footer-address h4 {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: #fff;
}

.footer-address p {
  font-size: 12px;
  line-height: 1.6;
  margin-bottom: 1rem;
  opacity: 0.8;
  text-transform: none;
  font-weight: 400;
}

.footer-address p:last-child {
  margin-bottom: 0;
}

/* Footer Bottom */
.footer-bottom {
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.footer-bottom p {
  font-size: 1.2rem;
  opacity: 0.6;
  text-transform: none;
  font-weight: 400;
}

.custom-navbar-menu {
  display: flex;
  flex-direction: row;
  /* gap: 20px; */
  padding: 20px 0;
  /* --text: #fff; */
  --text: #000;
  margin: 0;
  /* width: fit-content; */
  /* position: relative; */
  z-index: 2;
}

.custom-navbar-menu a {
  display: inline-flex;
  gap: 9px;
  padding: 0 1em 0 0;
  text-decoration: none;
  /* font-family: "Space Grotesk", "PP Neue Montreal", sans-serif; */
  font-size: 1.5rem;
  overflow: hidden;
  white-space: nowrap;
}

.custom-navbar-menu a i,
.custom-navbar-menu a span {
  color: var(--text);
  transition: all 0.5s ease;
  text-shadow: 0 1.1em 0 var(--text);
}

.custom-navbar-menu a span {
  font-weight: 400;
  transition: all 0.4s ease;
}

.custom-navbar-menu a i,
.custom-navbar-menu a span {
  color: var(--text);
  transition: all 0.5s ease;
  text-shadow: 0 1.1em 0 var(--text);
}

.custom-navbar-menu a:hover i,
.custom-navbar-menu a:hover span {
  color: transparent;
  transform: translate(0, -1.1em);
}

/* Custom Menu Nav for Fullscreen Menu */
.custom-menu-nav a {
  color: white;
  text-decoration: none;
  font-size: clamp(3rem, 6vw, 6rem);
  font-weight: 700;
  line-height: 1.1;
  display: block;
  transition: opacity 0.3s ease;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.custom-menu-nav a span {
  color: white;
  display: block;
  position: relative;
  overflow: hidden;
  height: 1.2em;
}

.custom-menu-nav .text-original,
.custom-menu-nav .text-hover {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

/* ABOUT PAGE STYLES */
.about-page {
  background: #fff;
}

/* Show main content on about page (no preloader) */
.about-page .main-content {
  display: block !important;
  opacity: 1;
  transform: translateY(0);
}

.about-page .navbar {
  color: #111;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.about-page .navbar.hide {
  opacity: 0;
  transform: translateY(-100%);
}

.about-page .navbar-menu a {
  color: #111;
}

.about-page .navbar-menu a:hover {
  opacity: 0.7;
}

/* About page menu and contact button behavior */
.about-page .menu-button {
  color: #111;
  border: 1px solid #111;
  background: rgba(255, 255, 255, 0.9);
}

.about-page .menu-button:hover {
  background: #111;
  color: #fff;
}

.about-page .contact-button {
  color: #111;
  border: 1px solid #111;
  background: rgba(255, 255, 255, 0.9);
}

.about-page .contact-button:hover {
  background: #111;
  color: #fff;
}

.about-page .contact-button span {
  color: #111;
}

.about-page .contact-button:hover span {
  color: #fff;
}

/* About page navbar get-in-touch button */
.about-page .get-in-touch-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #111;
  border: 1px solid #111;
}

.about-page .get-in-touch-btn span {
  color: #080807;
}

.about-page .get-in-touch-btn:hover {
  background: #080807;
  color: #fff;
}

.about-page .get-in-touch-btn:hover span {
  color: #fff;
}

.about-main {
  padding-top: 0;
}

/* Ensure about page content is visible */
.about-page .about-hero,
.about-page .about-content {
  display: block;
  opacity: 1;
}

/* About Hero Section */
.about-hero {
  /* min-height: 100vh; */
  /* height: 100vh; */
  /* display: flex; */
  /* align-items: center; */
  padding: 18rem 4rem 4rem;
  background: #fff;
  position: relative;
  padding-bottom: 70vh;
}

.about-hero-content {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  /* display: grid; */
  position: relative;
}

.about-hero-text {
  padding-right: 2rem;
}

.about-title {
  text-align: center;
  font-size: clamp(4rem, 15vw, 362px);
  font-weight: 700;
  line-height: 1.1;
  color: #111;
}

.about-subtitle {
  font-size: clamp(1.2rem, 2.5vw, 1.8rem);
  line-height: 1.6;
  color: #666;
  font-weight: 400;
}

.about-hero-image {
  position: absolute;
  top: 80%;
  left: 20%;
  /* transform: translate(-50%, -50%); */

  width: 60%;
  height: 70vh;
  min-height: 500px;
  overflow: hidden;
  border-radius: 0;
}

.about-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: grayscale(20%);
  transition: all 0.3s ease;
}

.about-hero-image:hover img {
  filter: grayscale(0%);
  transform: scale(1.02);
}

/* About Content Sections */
.about-content {
  /* padding: 8rem 4rem; */
  /* max-width: 1400px; */
  margin: 0 auto;
}

.about-section {
  margin-bottom: 8rem;
  padding-bottom: 4rem;
  border-bottom: 1px solid #eee;
}

.about-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.about-section-content {
  max-width: 1000px;
  text-align: center;
  margin: 0 auto;
  padding: 0 4rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 44px;
  line-height: 1.1;
  font-weight: 700;
  text-transform: none;
  color: #000;
}

.about-section h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  margin-bottom: 2rem;
  color: #111;
}

/* .about-section p {
  font-size: 1.4rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 1.5rem;
} */

.about-section p:last-child {
  margin-bottom: 0;
}

/* About Page Responsive */
@media (max-width: 1024px) {
  .about-hero {
    padding: 6rem 3rem 3rem;
  }

  .about-hero-content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .about-hero-text {
    padding-right: 0;
    text-align: center;
  }

  .about-hero-image {
    height: 50vh;
    min-height: 400px;
  }

  .about-content {
    padding: 6rem 3rem;
  }

  .about-section {
    margin-bottom: 6rem;
  }
}

@media (max-width: 768px) {
  .about-hero {
    padding: 4rem 2rem 2rem;
    padding-bottom: 50vh;
  }

  .about-hero-content {
    gap: 3rem;
  }

  .about-title {
    font-size: clamp(3rem, 12vw, 8rem) !important;
  }

  .about-hero-image {
    height: 40vh;
    min-height: 300px;
    top: 70%;
    left: 10%;
    width: 80%;
  }

  .about-content {
    padding: 4rem 2rem;
  }

  .about-section {
    margin-bottom: 4rem;
  }

  .about-section h2 {
    margin-bottom: 1.5rem;
  }

  .about-section p {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .about-hero {
    padding: 3rem 1rem 1rem;
    padding-bottom: 40vh;
  }

  .about-title {
    font-size: clamp(2.5rem, 10vw, 5rem) !important;
  }

  .about-hero-image {
    height: 35vh;
    min-height: 250px;
    top: 65%;
    left: 5%;
    width: 90%;
  }

  .about-content {
    padding: 3rem 1rem;
  }

  .about-section-content {
    padding: 0 1rem;
    font-size: 32px;
  }
}

/* Active state for about page navbar */
.about-page .navbar-menu a.active {
  opacity: 1;
  color: #2a4d3b;
  font-weight: 600;
}

/* Section Separator */

/* CTA SECTION */
.cta-section {
  padding: 8rem 4rem;
  background: #fff;
  color: #111;
  text-align: center;
}

/* CTA Button Specific Styling */
.cta-button {
  display: inline-flex;
  padding: 1.2rem 3rem;
  background: #111;
  color: #fff;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.4rem;
  border-radius: 50px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  overflow: hidden;
  position: relative;
}

.cta-button span {
  position: relative;
  z-index: 2;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  color: #fff;
}

.cta-button:hover {
  background: #333;
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.cta-button:hover span {
  transform: translateY(-2px);
}

.cta-container {
  max-width: 1200px;
  margin: 0 auto;
}

.cta-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
}

.cta-title {
  font-weight: 700;
  line-height: 1.1;
  letter-spacing: -0.02em;
  color: #000;
  text-align: center;
  max-width: 800px;
  font-size: 42px;
}

/* CTA button in CTA section (about page) */
.cta-button.get-in-touch-btn {
  /* background: black !important; */
  border: 2px solid #fff !important;
  /* color: #fff !important; */
  padding: 1.2rem 3rem !important;
  font-size: 1.1rem !important;
  font-weight: 500;
  letter-spacing: 0.02em;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  border-radius: 0 !important;
  backdrop-filter: none !important;
}

.cta-button.get-in-touch-btn span {
  color: #fff !important;
}

.cta-button.get-in-touch-btn:hover {
  background: #080807 !important;
  color: #fff !important;
  transform: translateY(-2px);
}

.cta-button.get-in-touch-btn:hover span {
  color: #fff !important;
}

.cta-button span {
  position: relative;
  z-index: 1;
}

/* CTA Section Responsive */
@media (max-width: 1024px) {
  .cta-section {
    padding: 6rem 3rem;
  }

  .cta-content {
    gap: 3rem;
  }
}

@media (max-width: 768px) {
  .cta-section {
    padding: 4rem 2rem;
  }

  .cta-content {
    gap: 2.5rem;
  }

  .cta-section .cta-button.get-in-touch-btn {
    padding: 1rem 2.5rem !important;
    font-size: 1rem !important;
  }
}

/* FOOTER */

/* Footer Copyright */

/* WHAT WE OFFER SECTION */
.services-offer-section {
  padding: 8rem 4rem;
  background: #f8f8f8;
}

.services-offer-container {
  max-width: 1400px;
  margin: 0 auto;
}

.services-offer-header {
  text-align: center;
  margin-bottom: 6rem;
}

.services-offer-title {
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  color: #111;
  margin-bottom: 2rem;
  letter-spacing: -0.02em;
}

.services-offer-subtitle {
  font-size: 1.6rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.services-offer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 4rem;
  margin-top: 4rem;
}

.service-offer-item {
  background: white;
  padding: 3rem;
  border-radius: 0;
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  position: relative;
  overflow: hidden;
  text-decoration: none;
  color: inherit;
  display: block;
}

.service-offer-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  border-left-color: #111;
}

.service-offer-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(17, 17, 17, 0.02) 0%,
    rgba(17, 17, 17, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-offer-item:hover::before {
  opacity: 1;
}

.service-offer-icon {
  margin-bottom: 2rem;
}

.service-offer-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: #111;
  color: white;
  font-size: 1.4rem;
  font-weight: 700;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.service-offer-item:hover .service-offer-number {
  background: #333;
  transform: scale(1.1);
}

.service-offer-content h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 1.5rem;
  letter-spacing: -0.01em;
}

.service-offer-description {
  font-size: 1.4rem;
  line-height: 1.7;
  color: #666;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .services-offer-section {
    padding: 6rem 3rem;
  }

  .services-offer-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
  }

  .service-offer-item {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .services-offer-section {
    padding: 4rem 2rem;
  }

  .services-offer-header {
    margin-bottom: 4rem;
  }

  .services-offer-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .service-offer-item {
    padding: 2rem;
  }

  .services-offer-subtitle {
    font-size: 1.4rem;
  }

  .service-offer-content h3 {
    font-size: 1.6rem;
  }

  .service-offer-description {
    font-size: 1.3rem;
  }
}

/* TEAM SECTION */
.team-section {
  padding: 8rem 4rem;
  background: #fff;
  margin-top: 0;
  /* border-top: 1px solid #eee; */
  position: relative;
  z-index: 1;
  clear: both;
}

.team-container {
  max-width: 1400px;
  margin: 0 auto;
}

.team-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8rem;
  align-items: flex-start;
}

.team-left {
  padding-right: 2rem;
}

.team-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 600;
  margin-bottom: 4rem;
  color: #111;
}

.team-members {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.team-member {
  padding: 2rem 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  opacity: 0.6;
}

.team-member:last-child {
  border-bottom: none;
}

.member-name {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #111;
}

.member-role {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 1rem;
  font-weight: 500;
}

.member-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #777;
}

.team-right {
  position: relative;
  height: 600px;
}

.team-image-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.team-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
}

/* Team Section Responsive */
@media (max-width: 1024px) {
  .team-section {
    padding: 6rem 3rem;
  }

  .team-content {
    grid-template-columns: 1fr;
    gap: 4rem;
  }

  .team-left {
    padding-right: 0;
  }

  .team-right {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .team-section {
    padding: 4rem 2rem;
  }

  .team-content {
    gap: 3rem;
  }

  .team-members {
    gap: 2rem;
  }

  .team-member {
    padding: 1.5rem 0;
  }

  .team-right {
    height: 400px;
  }

  .member-name {
    font-size: 1.5rem;
  }

  .member-description {
    font-size: 1rem;
  }
}

/* About Second Section Responsive */
@media (max-width: 1024px) {
  .second-about {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 3rem 0;
  }

  .about-second-section-content {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  .second-about {
    gap: 2rem;
    padding: 2rem 0;
  }

  .about-second-section-content {
    font-size: 28px;
    margin-top: 1.5rem;
  }
}

/* CONTACT SIDEBAR - MODERN DESIGN */
.contact-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.contact-sidebar.active {
  opacity: 1;
  pointer-events: auto;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  transition: background 0.3s ease;
}

.contact-sidebar-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 480px;
  height: 100vh;
  background: #ffffff;
  box-shadow: -10px 0 40px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.contact-sidebar.active .contact-sidebar-content {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1.5rem 2rem;
  border-bottom: 1px solid #f0f0f0;
  background: #ffffff;
}

.sidebar-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #111;
  margin: 0;
  letter-spacing: -0.01em;
}

/* Sidebar Body */
.sidebar-body {
  padding: 2rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.sidebar-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

/* Close Button */
.sidebar-close {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24px;
  font-weight: 300;
  transition: all 0.2s ease;
  line-height: 1;
}

.sidebar-close:hover {
  background: #f5f5f5;
  color: #333;
  transform: scale(1.1);
}

/* Contact Form */
.contact-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  flex: 1;
}

/* Input Groups */
.input-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.input-group input,
.input-group select,
.input-group textarea {
  width: 100%;
  padding: 1rem 1rem 1rem 1rem;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  font-size: 1rem;
  background: #ffffff;
  transition: all 0.3s ease;
  outline: none;
  font-family: inherit;
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
  border-color: #111;
  box-shadow: 0 0 0 3px rgba(17, 17, 17, 0.1);
}

.input-group label {
  position: absolute;
  left: 1rem;
  top: 1rem;
  font-size: 1rem;
  color: #999;
  pointer-events: none;
  transition: all 0.3s ease;
  background: #ffffff;
  padding: 0 0.25rem;
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label,
.input-group select:focus + label,
.input-group select:not([value=""]) + label,
.input-group textarea:focus + label,
.input-group textarea:not(:placeholder-shown) + label {
  top: -0.5rem;
  left: 0.75rem;
  font-size: 0.85rem;
  color: #111;
  font-weight: 500;
}

.input-group select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

.input-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Submit Button */
.submit-btn {
  width: 100%;
  background: #111;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-btn:hover {
  background: #333;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.submit-btn:active {
  transform: translateY(0);
}

/* Contact Form Styles */
.contact-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  margin-bottom: 2rem;
  align-self: flex-end;
  transition: color 0.3s ease;
}

.contact-close:hover {
  color: #111;
}

.contact-sidebar h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 1rem;
}

.contact-sidebar p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 3rem;
  line-height: 1.6;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #111;
}

.submit-btn {
  background: #111;
  color: white;
  border: none;
  padding: 1.2rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submit-btn:hover {
  background: #333;
  transform: translateY(-2px);
}

/* TESTIMONIALS PAGE STYLES */
.testimonials-page {
  background: #ffffff;
}

.testimonials-page .new-container {
  display: block !important;
  visibility: visible;
}

.testimonials-page .main-content {
  display: block !important;
  visibility: visible;
}

.testimonials-page .footer {
  display: block !important;
}

/* Testimonials Hero */
.testimonials-hero {
  padding: 12rem 2rem 4rem 2rem;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 1;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto 4rem auto;
}

.hero-title {
  font-size: 4rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 1.5rem;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.3rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
  max-width: 600px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 0.5rem;
  letter-spacing: -0.02em;
}

.stat-label {
  font-size: 1rem;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

/* Featured Testimonial */
.featured-testimonial {
  padding: 6rem 2rem;
  background: #ffffff;
}

.featured-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.featured-quote blockquote {
  font-size: 1.8rem;
  line-height: 1.4;
  color: #111;
  margin: 0 0 2rem 0;
  font-weight: 400;
  font-style: italic;
  position: relative;
}

.featured-quote blockquote::before {
  content: '"';
  font-size: 4rem;
  color: #e0e0e0;
  position: absolute;
  top: -1rem;
  left: -2rem;
  font-family: serif;
}

.featured-author {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.author-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #111;
  margin: 0 0 0.25rem 0;
}

.author-info p {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

.author-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.author-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.featured-project {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.featured-project img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-project:hover img {
  transform: scale(1.05);
}

/* Testimonials Grid */
.testimonials-grid {
  padding: 6rem 2rem;
  background: #f8f9fa;
}

.grid-header {
  text-align: center;
  max-width: 600px;
  margin: 0 auto 4rem auto;
}

.grid-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.grid-header p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  margin: 0;
}

.testimonials-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

/* Testimonial Card */
.testimonial-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 2em;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.testimonial-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #111 0%, #666 100%);
}

.testimonial-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.testimonial-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333;
  margin-bottom: 2rem;
  font-style: italic;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.testimonial-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.testimonial-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-info h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #111;
  margin: 0 0 0.25rem 0;
}

.testimonial-info p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.testimonial-rating {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.star {
  color: #ffd700;
  font-size: 1rem;
}

/* Testimonials CTA */
.testimonials-cta {
  padding: 6rem 2rem;
  background: #111;
  color: #ffffff;
  text-align: center;
}

.testimonials-cta .cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.testimonials-cta h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
  line-height: 1.2;
}

.testimonials-cta p {
  font-size: 1.2rem;
  color: #cccccc;
  margin-bottom: 2.5rem;
  line-height: 1.6;
}

.testimonials-cta .cta-button {
  background: #ffffff;
  color: #111;
  border: none;
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.testimonials-cta .cta-button:hover {
  background: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

/* Process page responsive styles */
@media (max-width: 768px) {
  #intro {
    padding: 6rem 2rem 3rem;
  }

  #intro h1 {
    font-size: clamp(1.8rem, 5vw, 2.5rem);
    line-height: 1.2;
  }

  #intro p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-top: 2rem;
  }

  #intro img {
    width: 90vw;
    margin: 2rem 0;
  }

  .grid {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 80px;
  }

  .pin-content-list {
    gap: 0;
  }

  .pin-image-sticky {
    position: relative;
    top: auto;
    height: 50vh;
  }

  .px-5 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .parallax-text h2 {
    font-size: clamp(2rem, 6vw, 2.5rem);
    line-height: 1.2;
  }

  .parallax-text p {
    font-size: 1.2rem;
    line-height: 1.6;
  }

  .col-8 {
    width: 100%;
  }

  .offset-2 {
    margin-left: 0;
  }

  .pin-text {
    padding: 2rem 1.5rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
  }

  .pin-text h2 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    margin-bottom: 1rem;
  }

  .pin-text p {
    font-size: 1rem;
    line-height: 1.5;
  }

  /* Adjust left border accent for mobile */
  .pin-text::before {
    width: 3px;
    height: 40px;
  }

  .pin-text h2,
  .pin-text p {
    padding-left: 15px;
  }
}

@media (max-width: 480px) {
  #intro {
    padding: 4rem 1rem 2rem;
  }

  #intro h1 {
    font-size: clamp(1.5rem, 4vw, 1.8rem);
  }

  #intro p {
    font-size: 1rem;
  }

  .pin-text {
    padding: 1.5rem 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
  }

  .pin-text h2 {
    font-size: clamp(1.3rem, 3.5vw, 1.5rem);
  }

  .pin-text p {
    font-size: 0.9rem;
  }

  /* Further adjust left border accent for very small screens */
  .pin-text::before {
    width: 2px;
    height: 30px;
  }

  .pin-text h2,
  .pin-text p {
    padding-left: 12px;
  }

  .parallax-text h2 {
    font-size: clamp(1.8rem, 5vw, 2rem);
  }

  .parallax-text p {
    font-size: 1rem;
  }
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem;
  border-bottom: 1px solid #eee;
  background: #f8f8f8;
}

.sidebar-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: #111;
  margin: 0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-header h2:hover {
  color: #2a4d3b;
  transform: translateY(-2px);
}

.sidebar-close {
  background: none;
  border: none;
  font-size: 1.4rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem 1rem;
  line-height: 1;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.sidebar-close:hover {
  color: #111;
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.contact-form {
  flex: 1;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section h3 {
  font-size: 1.4rem;
  font-weight: 600;
  color: #111;
  margin-bottom: 1.5rem;
  cursor: pointer;
  width: fit-content;
  transition: all 0.3s ease;
}

.form-section h3:hover {
  color: #2a4d3b;
  transform: translateX(5px);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-group input {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: #fff;
}

.form-group input:focus {
  outline: none;
  border-color: #2a4d3b;
}

.requirement-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.option-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}

.option-card:hover {
  border-color: #2a4d3b;
  background: #f8f9fa;
}

.option-card input[type="radio"] {
  margin-right: 1rem;
  width: 18px;
  height: 18px;
  accent-color: #2a4d3b;
}

.option-card input[type="radio"]:checked + .option-text {
  color: #2a4d3b;
  font-weight: 600;
}

.option-text {
  font-size: 1.1rem;
  color: #333;
  transition: all 0.3s ease;
}

.submit-btn {
  background: #2a4d3b;
  color: #fff;
  border: none;
  padding: 1.2rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  margin-top: auto;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(42, 77, 59, 0.2);
}

.submit-btn:hover {
  background: #111;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(42, 77, 59, 0.3);
}

/* ===== NEW NAVBAR AND HERO STYLES ===== */

/* New Navbar Styles */
.new-navbar {
  position: fixed;
  width: 100vw;
  padding: 2em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1000;
  font-family: "Inter", "PP Neue Montreal", sans-serif;
}

.new-navbar.scrolled .new-logo {
  opacity: 0;
  pointer-events: none;
}

.new-navbar .new-logo {
  pointer-events: auto;
  transition: opacity 0.3s ease;
}

.new-navbar .new-logo a {
  font-weight: 600;
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
}

.new-navbar .new-logo img {
  height: 40px;
  width: auto;
  object-fit: contain;
  /* filter: brightness(0) invert(1); Make logo white */
}

/* Remove old navbar buttons container */

.new-menu-toggle {
  position: relative;
  width: 3rem;
  height: 1.5rem;
  cursor: pointer;
  pointer-events: auto; /* Enable clicks on menu toggle */
}

.new-menu-toggle p {
  position: absolute;
  transform-origin: top left;
  will-change: transform, opacity;
  color: #fff;
  font-size: 1rem;
  font-weight: 300;
  user-select: none;
  margin: 0;
}

/* Override for sticky menu toggle positioning */
.new-navbar .new-menu-toggle {
  position: relative !important; /* Reset for navbar context */
  width: auto !important;
  height: auto !important;
  pointer-events: auto;
}

/* New Menu Overlay Styles */
.new-menu-overlay {
  position: fixed;
  width: 100vw;
  height: 100svh;
  background-color: #0f0f0f;
  z-index: 999;
  font-family: "Inter", "PP Neue Montreal", sans-serif;
}

.new-menu-overlay .new-menu-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: left bottom;
  will-change: transform, opacity;
}

.new-menu-items,
.new-menu-footer {
  width: 100%;
  padding: 2.5em;
  display: flex;
  gap: 2.5em;
}

.col-lg {
  flex: 3;
}

.col-sm {
  flex: 2;
}

.new-menu-items .col-lg {
  display: flex;
  justify-content: center;
  align-items: center;
}

.new-menu-preview-img {
  position: relative;
  width: 45%;
  height: 60vh;
  overflow: hidden;
  border-radius: 8px;
}

.new-menu-preview-img img {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform, opacity;
}

.new-menu-items .col-sm {
  padding: 2.5em 0;
  display: flex;
  flex-direction: column;
  gap: 2.5em;
}

.new-menu-links,
.new-menu-socials {
  display: flex;
  flex-direction: column;
  gap: 0.5em;
}

.new-link,
.new-social {
  padding-bottom: 6px;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}

.new-link a,
.new-social a {
  display: inline-block;
  will-change: transform;
  transition: color 0.5s;
  color: #fff;
  text-decoration: none;
  position: relative;
}

.new-link a {
  font-size: 3.5rem;
  letter-spacing: -0.02rem;
  font-weight: 400;
}

.new-social a {
  color: #8f8f8f;
  font-size: 1rem;
  font-weight: 300;
}

.new-social a:hover {
  color: #fff;
}

.new-menu-footer {
  position: absolute;
  bottom: 0;
}

.new-menu-footer .col-sm {
  display: flex;
  justify-content: space-between;
}

.new-menu-footer a {
  color: #fff;
  text-decoration: none;
  font-size: 1rem;
  font-weight: 300;
  position: relative;
}

.new-link a::after,
.new-social a::after,
.new-menu-footer a::after {
  position: absolute;
  content: "";
  top: 102.5%;
  left: 0;
  width: 100%;
  height: 2px;
  background: #fff;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s cubic-bezier(0.6, 0, 0.4, 1);
}

.new-link a:hover::after,
.new-social a:hover::after,
.new-menu-footer a:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* New Container for 3D Transformation */
.new-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  will-change: transform;
  transform-origin: right top;
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 1;
}

/* New Hero Section */
.new-hero {
  position: relative;
  width: 100vw;
  height: 100svh;
  padding: 2.5em;
  display: flex;
  align-items: flex-end;
  overflow: hidden;
}

.new-hero-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100svh;
  z-index: -1;
}

.new-hero-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.new-hero h1 {
  color: #fff;
  font-size: 7rem;
  font-weight: 400;
  letter-spacing: -0.02rem;
  line-height: 1;
  text-shadow: 2px 1px #111;
  width: 80%;
  font-family: "Inter", "PP Neue Montreal", sans-serif;
}

/* Responsive adjustments for new navbar and hero */
@media (max-width: 900px) {
  .new-hero h1 {
    width: 100%;
    font-size: clamp(3rem, 8vw, 4rem);
    letter-spacing: 0;
  }

  .new-menu-items .col-lg {
    display: none;
  }

  .new-link a::after,
  .new-social a::after,
  .new-menu-footer a::after {
    display: none;
  }

  .new-navbar {
    padding: 2rem;
  }

  .new-hero {
    padding: 2rem;
  }

  .new-link a {
    font-size: 2.5rem;
  }
}

@media (max-width: 600px) {
  .new-hero {
    padding: 1.5rem;
  }

  .new-hero h1 {
    font-size: clamp(2.5rem, 7vw, 3.5rem);
    width: 100%;
  }

  .new-link a {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .new-hero {
    padding: 1rem;
  }

  .new-hero h1 {
    font-size: clamp(2rem, 6vw, 3rem);
  }

  .new-link a {
    font-size: 1.8rem;
  }
}

/* Old contact button styles removed - using new sticky button styles below */

/* Hide legacy navbar elements */
.navbar {
  display: none !important;
}

/* Hide old menu button styles */
.menu-button {
  display: none !important;
}

/* Sticky Buttons Container */
.sticky-buttons-container {
  position: fixed !important;
  top: 2.5rem !important;
  right: 2.5rem !important;
  z-index: 1002 !important;
  display: flex !important;
  gap: 1rem !important;
  align-items: center !important;
}

/* Get in Touch Button */
.sticky-buttons-container .contact-button,
.sticky-buttons-container .get-in-touch-btn,
button.get-in-touch-btn,
#get-in-touch-btn {
  background: #080807 !important;
  color: white !important;
  border: none !important;
  padding: 1rem 1.5rem !important;
  border-radius: 50px !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  display: inline-block !important;
  font-family: "Inter", "PP Neue Montreal", sans-serif !important;
  box-shadow: 0 4px 20px rgba(42, 77, 59, 0.3) !important;
  white-space: nowrap !important;
}

.sticky-buttons-container .contact-button:hover,
.sticky-buttons-container .get-in-touch-btn:hover,
button.get-in-touch-btn:hover,
#get-in-touch-btn:hover {
  background: #080807 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 25px rgba(42, 77, 59, 0.4) !important;
}

.sticky-buttons-container .contact-button span,
.sticky-buttons-container .get-in-touch-btn span,
button.get-in-touch-btn span,
#get-in-touch-btn span {
  color: white !important;
  font-weight: 500;
  pointer-events: none !important;
}

/* Menu Button */
.sticky-buttons-container .sticky-menu-btn,
button.sticky-menu-btn,
#sticky-menu-btn {
  background: rgba(128, 128, 128, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(128, 128, 128, 0.3) !important;
  border-radius: 50px !important;
  padding: 1rem 1.5rem !important;
  color: #fff !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  font-family: "Inter", "PP Neue Montreal", sans-serif !important;
  display: inline-block !important;
  white-space: nowrap !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.sticky-buttons-container .sticky-menu-btn:hover,
button.sticky-menu-btn:hover,
#sticky-menu-btn:hover {
  background: rgba(96, 96, 96, 0.95) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Force hover animations to work on all pages */
.sticky-buttons-container button:hover {
  transform: translateY(-2px) !important;
}

/* Duplicate styles removed */

/* Responsive Styles */
@media (max-width: 900px) {
  .new-navbar {
    padding: 2rem;
  }

  .sticky-buttons-container {
    top: 2rem !important;
    right: 2rem !important;
    gap: 0.8rem !important;
  }

  .sticky-buttons-container .contact-button,
  .sticky-buttons-container .get-in-touch-btn,
  .sticky-buttons-container .sticky-menu-btn {
    padding: 0.8rem 1.2rem !important;
    font-size: 0.9rem !important;
  }
}

@media (max-width: 600px) {
  .new-navbar {
    padding: 1.5rem;
  }

  .sticky-buttons-container {
    top: 1.5rem !important;
    right: 1.5rem !important;
    gap: 0.7rem !important;
  }

  .sticky-buttons-container .contact-button,
  .sticky-buttons-container .get-in-touch-btn,
  .sticky-buttons-container .sticky-menu-btn {
    padding: 0.7rem 1rem !important;
    font-size: 0.8rem !important;
  }
}

@media (max-width: 480px) {
  .new-navbar {
    padding: 1rem;
  }

  .sticky-buttons-container {
    top: 1rem !important;
    right: 1rem !important;
    gap: 0.6rem !important;
  }

  .sticky-buttons-container .contact-button,
  .sticky-buttons-container .get-in-touch-btn,
  .sticky-buttons-container .sticky-menu-btn {
    padding: 0.6rem 0.8rem !important;
    font-size: 0.75rem !important;
  }

  /* Ensure buttons don't overlap on very small screens */
}

@media (max-width: 360px) {
  .sticky-buttons-container .contact-button,
  .sticky-buttons-container .get-in-touch-btn,
  .sticky-buttons-container .sticky-menu-btn {
    padding: 0.5rem 0.7rem !important;
    font-size: 0.7rem !important;
  }
}
